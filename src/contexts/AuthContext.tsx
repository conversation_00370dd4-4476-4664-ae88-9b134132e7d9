'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authApi, AuthResponse, User, LoginRequest, RegisterRequest } from '@/lib/api';
import { authUtils, errorUtils } from '@/lib/auth';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = authUtils.getToken();
        const storedUser = authUtils.getUser();

        if (token && storedUser) {
          // Verify token is still valid by fetching user profile
          try {
            const currentUser = await authApi.getUserProfile();
            setUser(currentUser);
          } catch (error) {
            // Token is invalid, clear auth data
            authUtils.clearAuth();
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        authUtils.clearAuth();
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      setIsLoading(true);
      const authResponse: AuthResponse = await authApi.login(credentials);
      
      // Store auth data
      authUtils.setAuth(authResponse);
      
      // Fetch full user profile
      const userProfile = await authApi.getUserProfile();
      setUser(userProfile);
      
      toast.success('Login successful!');
    } catch (error) {
      const errorMessage = errorUtils.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterRequest): Promise<void> => {
    try {
      setIsLoading(true);
      const authResponse: AuthResponse = await authApi.register(userData);
      
      // Store auth data
      authUtils.setAuth(authResponse);
      
      // Fetch full user profile
      const userProfile = await authApi.getUserProfile();
      setUser(userProfile);
      
      toast.success('Registration successful!');
    } catch (error) {
      const errorMessage = errorUtils.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = (): void => {
    authUtils.clearAuth();
    setUser(null);
    toast.success('Logged out successfully');
    window.location.href = '/auth/login';
  };

  const updateUser = async (userData: Partial<User>): Promise<void> => {
    try {
      const updatedUser = await authApi.updateProfile(userData);
      setUser(updatedUser);
      toast.success('Profile updated successfully');
    } catch (error) {
      const errorMessage = errorUtils.getErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      const userProfile = await authApi.getUserProfile();
      setUser(userProfile);
    } catch (error) {
      console.error('Error refreshing user:', error);
      // If refresh fails, user might be logged out
      if (errorUtils.isAuthError(error)) {
        logout();
      }
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'ADMIN',
    login,
    register,
    logout,
    updateUser,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
