'use client';

import React, { useState } from 'react';
import { authApi } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

export default function TestPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (test: string, success: boolean, data?: any, error?: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      data,
      error,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testBackendConnection = async () => {
    setIsLoading(true);
    setTestResults([]);

    // Test 1: Admin Login
    try {
      const adminResult = await authApi.login({
        username: 'admin',
        password: 'admin123'
      });
      addResult('Admin Login', true, adminResult);
      toast.success('Admin login successful!');
    } catch (error) {
      addResult('Admin Login', false, null, error);
      toast.error('Admin login failed');
    }

    // Test 2: User Login
    try {
      const userResult = await authApi.login({
        username: 'testuser2',
        password: 'password123'
      });
      addResult('User Login', true, userResult);
      toast.success('User login successful!');
    } catch (error) {
      addResult('User Login', false, null, error);
      toast.error('User login failed');
    }

    // Test 3: Invalid Login
    try {
      await authApi.login({
        username: 'invalid',
        password: 'invalid'
      });
      addResult('Invalid Login', false, null, 'Should have failed but succeeded');
    } catch (error) {
      addResult('Invalid Login', true, null, 'Correctly rejected invalid credentials');
    }

    setIsLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Backend Connection Test</h1>
          <p className="text-gray-600">
            Test the connection and authentication with the backend API
          </p>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>API Tests</CardTitle>
            <CardDescription>
              Run tests to verify backend connectivity and authentication
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button 
                onClick={testBackendConnection}
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? 'Running Tests...' : 'Run Backend Tests'}
              </Button>
              <Button 
                variant="outline" 
                onClick={clearResults}
                disabled={isLoading}
              >
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                Results from backend API tests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{result.test}</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant={result.success ? 'default' : 'destructive'}>
                          {result.success ? 'PASS' : 'FAIL'}
                        </Badge>
                        <span className="text-sm text-gray-500">{result.timestamp}</span>
                      </div>
                    </div>
                    
                    {result.data && (
                      <div className="mt-2">
                        <h4 className="text-sm font-medium text-green-700 mb-1">Response Data:</h4>
                        <pre className="text-xs bg-green-50 p-2 rounded overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {result.error && (
                      <div className="mt-2">
                        <h4 className="text-sm font-medium text-red-700 mb-1">Error:</h4>
                        <pre className="text-xs bg-red-50 p-2 rounded overflow-x-auto">
                          {typeof result.error === 'string' 
                            ? result.error 
                            : JSON.stringify(result.error, null, 2)
                          }
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Backend Info */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Backend Information</CardTitle>
            <CardDescription>
              Configuration and connection details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">API Configuration</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Base URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1'}</p>
                  <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Test Credentials</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Admin:</strong> admin / admin123</p>
                  <p><strong>User:</strong> testuser2 / password123</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="mt-8 flex gap-4">
          <Button variant="outline" asChild>
            <a href="/">← Back to Home</a>
          </Button>
          <Button variant="outline" asChild>
            <a href="/auth/login">Go to Login</a>
          </Button>
          <Button variant="outline" asChild>
            <a href="/auth/register">Go to Register</a>
          </Button>
        </div>
      </div>
    </div>
  );
}
