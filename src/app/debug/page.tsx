'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { authUtils } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Cookies from 'js-cookie';

export default function DebugPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [cookieData, setCookieData] = useState<any>({});

  useEffect(() => {
    // Get all cookies
    const allCookies = Cookies.get();
    setCookieData(allCookies);
  }, []);

  const clearAllCookies = () => {
    authUtils.clearAuth();
    setCookieData({});
    window.location.reload();
  };

  const refreshData = () => {
    const allCookies = Cookies.get();
    setCookieData(allCookies);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Debug Information</h1>
          <p className="text-gray-600">
            Debug authentication state and cookies
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Auth Context State */}
          <Card>
            <CardHeader>
              <CardTitle>Auth Context State</CardTitle>
              <CardDescription>
                Current authentication state from React context
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Is Loading:</span>
                  <Badge variant={isLoading ? 'destructive' : 'default'}>
                    {isLoading ? 'True' : 'False'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Is Authenticated:</span>
                  <Badge variant={isAuthenticated ? 'default' : 'destructive'}>
                    {isAuthenticated ? 'True' : 'False'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">User Role:</span>
                  <Badge variant="outline">
                    {user?.role || 'None'}
                  </Badge>
                </div>
              </div>

              {user && (
                <div className="mt-6">
                  <h4 className="font-medium mb-2">User Data:</h4>
                  <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                    {JSON.stringify(user, null, 2)}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Cookies */}
          <Card>
            <CardHeader>
              <CardTitle>Browser Cookies</CardTitle>
              <CardDescription>
                Current cookies stored in the browser
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Auth Token:</span>
                  <Badge variant={cookieData.auth_token ? 'default' : 'destructive'}>
                    {cookieData.auth_token ? 'Present' : 'Missing'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">User Data:</span>
                  <Badge variant={cookieData.user_data ? 'default' : 'destructive'}>
                    {cookieData.user_data ? 'Present' : 'Missing'}
                  </Badge>
                </div>
              </div>

              {Object.keys(cookieData).length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium mb-2">All Cookies:</h4>
                  <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                    {JSON.stringify(cookieData, null, 2)}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Auth Utils */}
          <Card>
            <CardHeader>
              <CardTitle>Auth Utils</CardTitle>
              <CardDescription>
                Direct calls to auth utility functions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Utils - Is Authenticated:</span>
                  <Badge variant={authUtils.isAuthenticated() ? 'default' : 'destructive'}>
                    {authUtils.isAuthenticated() ? 'True' : 'False'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Utils - Is Admin:</span>
                  <Badge variant={authUtils.isAdmin() ? 'default' : 'destructive'}>
                    {authUtils.isAdmin() ? 'True' : 'False'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Utils - Token:</span>
                  <Badge variant={authUtils.getToken() ? 'default' : 'destructive'}>
                    {authUtils.getToken() ? 'Present' : 'Missing'}
                  </Badge>
                </div>
              </div>

              {authUtils.getUser() && (
                <div className="mt-6">
                  <h4 className="font-medium mb-2">Utils User Data:</h4>
                  <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                    {JSON.stringify(authUtils.getUser(), null, 2)}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Debug Actions</CardTitle>
              <CardDescription>
                Actions to help debug authentication issues
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button onClick={refreshData} className="w-full">
                  Refresh Data
                </Button>
                <Button onClick={clearAllCookies} variant="destructive" className="w-full">
                  Clear All Cookies
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <a href="/auth/login">Go to Login</a>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <a href="/">Go to Home</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Environment Info */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Environment Information</CardTitle>
            <CardDescription>
              Current environment and configuration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Environment</h4>
                <div className="text-sm space-y-1">
                  <p><strong>NODE_ENV:</strong> {process.env.NODE_ENV}</p>
                  <p><strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1'}</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Current Page</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Pathname:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'N/A'}</p>
                  <p><strong>Search:</strong> {typeof window !== 'undefined' ? window.location.search : 'N/A'}</p>
                  <p><strong>Host:</strong> {typeof window !== 'undefined' ? window.location.host : 'N/A'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
